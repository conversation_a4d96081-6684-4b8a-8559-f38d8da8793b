// Interactive Animations Module
(function() {
    'use strict';

    const interactiveAnimations = [
        {
            id: 'magnetic-button',
            title: 'Magnetic Button',
            description: '<PERSON><PERSON> follows mouse cursor with magnetic effect',
            category: 'interactive',
            tags: ['magnetic', 'mouse', 'interactive', 'gsap'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button" id="magnetic-${AnimationUtils.generateId()}">Magnetic</div>
                    </div>
                `;

                const button = container.querySelector('.demo-button');

                container.addEventListener('mousemove', (e) => {
                    const rect = button.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;

                    const deltaX = (e.clientX - centerX) * 0.3;
                    const deltaY = (e.clientY - centerY) * 0.3;

                    gsap.to(button, {
                        x: deltaX,
                        y: deltaY,
                        duration: 0.3,
                        ease: "power2.out"
                    });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(button, {
                        x: 0,
                        y: 0,
                        duration: 0.5,
                        ease: "elastic.out(1, 0.3)"
                    });
                });
            }
        },
        {
            id: 'cursor-trail',
            title: 'Cursor Trail',
            description: 'Animated particles follow the mouse cursor',
            category: 'interactive',
            tags: ['cursor', 'trail', 'particles', 'mouse'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden;">
                        <div class="demo-text">Move mouse here</div>
                    </div>
                `;

                const particles = [];
                const maxParticles = 10;

                container.addEventListener('mousemove', (e) => {
                    const rect = container.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    // Create particle
                    const particle = document.createElement('div');
                    particle.style.position = 'absolute';
                    particle.style.left = x + 'px';
                    particle.style.top = y + 'px';
                    particle.style.width = '6px';
                    particle.style.height = '6px';
                    particle.style.backgroundColor = AnimationUtils.randomColor();
                    particle.style.borderRadius = '50%';
                    particle.style.pointerEvents = 'none';
                    particle.style.zIndex = '10';

                    container.appendChild(particle);
                    particles.push(particle);

                    // Animate particle
                    gsap.to(particle, {
                        scale: 0,
                        opacity: 0,
                        duration: 1,
                        ease: 'power2.out',
                        onComplete: () => {
                            if (particle.parentNode) {
                                particle.parentNode.removeChild(particle);
                            }
                            const index = particles.indexOf(particle);
                            if (index > -1) {
                                particles.splice(index, 1);
                            }
                        }
                    });

                    // Limit particles
                    if (particles.length > maxParticles) {
                        const oldParticle = particles.shift();
                        if (oldParticle && oldParticle.parentNode) {
                            oldParticle.parentNode.removeChild(oldParticle);
                        }
                    }
                });
            }
        },
        {
            id: 'ripple-effect',
            title: 'Ripple Effect',
            description: 'Click to create expanding ripple animation',
            category: 'interactive',
            tags: ['ripple', 'click', 'expand', 'wave'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden; cursor: pointer;">
                        <div class="demo-card">Click for ripple</div>
                    </div>
                `;

                container.addEventListener('click', (e) => {
                    const rect = container.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.style.width = '0px';
                    ripple.style.height = '0px';
                    ripple.style.borderRadius = '50%';
                    ripple.style.backgroundColor = 'rgba(99, 102, 241, 0.3)';
                    ripple.style.transform = 'translate(-50%, -50%)';
                    ripple.style.pointerEvents = 'none';

                    container.appendChild(ripple);

                    gsap.to(ripple, {
                        width: '200px',
                        height: '200px',
                        opacity: 0,
                        duration: 0.6,
                        ease: 'power2.out',
                        onComplete: () => {
                            if (ripple.parentNode) {
                                ripple.parentNode.removeChild(ripple);
                            }
                        }
                    });
                });
            }
        },
        {
            id: 'parallax-card',
            title: 'Parallax Card',
            description: 'Card tilts and creates depth effect on mouse move',
            category: 'interactive',
            tags: ['parallax', '3d', 'tilt', 'depth'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card" id="parallax-${AnimationUtils.generateId()}"
                             style="transform-style: preserve-3d; perspective: 1000px;">
                            <div style="transform: translateZ(20px);">3D Card</div>
                        </div>
                    </div>
                `;

                const card = container.querySelector('.demo-card');

                container.addEventListener('mousemove', (e) => {
                    const rect = card.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;

                    const rotateX = (e.clientY - centerY) / 10;
                    const rotateY = (centerX - e.clientX) / 10;

                    gsap.to(card, {
                        rotationX: rotateX,
                        rotationY: rotateY,
                        duration: 0.3,
                        ease: 'power2.out'
                    });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(card, {
                        rotationX: 0,
                        rotationY: 0,
                        duration: 0.5,
                        ease: 'elastic.out(1, 0.3)'
                    });
                });
            }
        },
        {
            id: 'elastic-button',
            title: 'Elastic Button',
            description: 'Button with elastic bounce effect on click',
            category: 'interactive',
            tags: ['elastic', 'bounce', 'click', 'button'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button" id="elastic-${AnimationUtils.generateId()}">Bounce Me</div>
                    </div>
                `;

                const button = container.querySelector('.demo-button');

                button.addEventListener('click', () => {
                    gsap.to(button, {
                        scale: 1.2,
                        duration: 0.1,
                        ease: 'power2.out',
                        yoyo: true,
                        repeat: 1,
                        onComplete: () => {
                            gsap.to(button, {
                                scale: 1,
                                duration: 0.5,
                                ease: 'elastic.out(1, 0.3)'
                            });
                        }
                    });
                });
            }
        },
        {
            id: 'drag-element',
            title: 'Draggable Element',
            description: 'Element can be dragged around with smooth physics',
            category: 'interactive',
            tags: ['drag', 'draggable', 'physics', 'gsap'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative;">
                        <div class="demo-shape" id="drag-${AnimationUtils.generateId()}"
                             style="cursor: grab; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                        </div>
                    </div>
                `;

                const element = container.querySelector('.demo-shape');
                let isDragging = false;
                let startX, startY, initialX, initialY;

                element.addEventListener('mousedown', (e) => {
                    isDragging = true;
                    element.style.cursor = 'grabbing';

                    const rect = element.getBoundingClientRect();
                    startX = e.clientX;
                    startY = e.clientY;
                    initialX = rect.left;
                    initialY = rect.top;

                    e.preventDefault();
                });

                document.addEventListener('mousemove', (e) => {
                    if (!isDragging) return;

                    const deltaX = e.clientX - startX;
                    const deltaY = e.clientY - startY;

                    gsap.set(element, {
                        x: deltaX,
                        y: deltaY
                    });
                });

                document.addEventListener('mouseup', () => {
                    if (isDragging) {
                        isDragging = false;
                        element.style.cursor = 'grab';

                        // Snap back to center
                        gsap.to(element, {
                            x: 0,
                            y: 0,
                            duration: 0.5,
                            ease: 'elastic.out(1, 0.3)'
                        });
                    }
                });
            }
        },
        {
            id: 'hover-particles',
            title: 'Hover Particles',
            description: 'Particles emit from element on hover',
            category: 'interactive',
            tags: ['hover', 'particles', 'emit', 'burst'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden;">
                        <div class="demo-button" id="particle-btn-${AnimationUtils.generateId()}">Hover for Particles</div>
                    </div>
                `;

                const button = container.querySelector('.demo-button');
                let particleInterval;

                button.addEventListener('mouseenter', () => {
                    particleInterval = setInterval(() => {
                        const particle = document.createElement('div');
                        particle.style.position = 'absolute';
                        particle.style.width = '4px';
                        particle.style.height = '4px';
                        particle.style.backgroundColor = AnimationUtils.randomColor();
                        particle.style.borderRadius = '50%';
                        particle.style.pointerEvents = 'none';

                        const rect = button.getBoundingClientRect();
                        const containerRect = container.getBoundingClientRect();
                        particle.style.left = (rect.left - containerRect.left + rect.width / 2) + 'px';
                        particle.style.top = (rect.top - containerRect.top + rect.height / 2) + 'px';

                        container.appendChild(particle);

                        const angle = Math.random() * Math.PI * 2;
                        const distance = AnimationUtils.randomBetween(30, 60);
                        const x = Math.cos(angle) * distance;
                        const y = Math.sin(angle) * distance;

                        gsap.to(particle, {
                            x: x,
                            y: y,
                            scale: 0,
                            opacity: 0,
                            duration: 1,
                            ease: 'power2.out',
                            onComplete: () => {
                                if (particle.parentNode) {
                                    particle.parentNode.removeChild(particle);
                                }
                            }
                        });
                    }, 100);
                });

                button.addEventListener('mouseleave', () => {
                    if (particleInterval) {
                        clearInterval(particleInterval);
                    }
                });
            }
        },
        {
            id: 'morphing-cursor',
            title: 'Morphing Cursor',
            description: 'Custom cursor that morphs on hover',
            category: 'interactive',
            tags: ['cursor', 'morph', 'custom', 'follow'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; cursor: none;">
                        <div class="demo-text">Custom Cursor Area</div>
                        <div class="custom-cursor" style="position: absolute; width: 20px; height: 20px; background: var(--color-primary); border-radius: 50%; pointer-events: none; z-index: 10; transform: translate(-50%, -50%);"></div>
                    </div>
                `;

                const cursor = container.querySelector('.custom-cursor');
                const text = container.querySelector('.demo-text');

                container.addEventListener('mousemove', (e) => {
                    const rect = container.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    gsap.to(cursor, {
                        left: x,
                        top: y,
                        duration: 0.1,
                        ease: 'power2.out'
                    });
                });

                text.addEventListener('mouseenter', () => {
                    gsap.to(cursor, {
                        scale: 2,
                        backgroundColor: 'var(--color-accent)',
                        duration: 0.3,
                        ease: 'power2.out'
                    });
                });

                text.addEventListener('mouseleave', () => {
                    gsap.to(cursor, {
                        scale: 1,
                        backgroundColor: 'var(--color-primary)',
                        duration: 0.3,
                        ease: 'power2.out'
                    });
                });
            }
        },
        {
            id: 'squeeze-button',
            title: 'Squeeze Button',
            description: 'Button squeezes and expands on interaction',
            category: 'interactive',
            tags: ['squeeze', 'button', 'press', 'elastic'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button" id="squeeze-${AnimationUtils.generateId()}">Squeeze Me</div>
                    </div>
                `;

                const button = container.querySelector('.demo-button');

                button.addEventListener('mousedown', () => {
                    gsap.to(button, {
                        scaleX: 0.9,
                        scaleY: 1.1,
                        duration: 0.1,
                        ease: 'power2.out'
                    });
                });

                button.addEventListener('mouseup', () => {
                    gsap.to(button, {
                        scaleX: 1,
                        scaleY: 1,
                        duration: 0.3,
                        ease: 'elastic.out(1, 0.3)'
                    });
                });

                button.addEventListener('mouseleave', () => {
                    gsap.to(button, {
                        scaleX: 1,
                        scaleY: 1,
                        duration: 0.3,
                        ease: 'elastic.out(1, 0.3)'
                    });
                });
            }
        },
        {
            id: 'liquid-button',
            title: 'Liquid Button',
            description: 'Button with liquid-like hover effect',
            category: 'interactive',
            tags: ['liquid', 'button', 'morph', 'organic'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button" id="liquid-${AnimationUtils.generateId()}"
                             style="border-radius: 25px; position: relative; overflow: hidden;">
                            Liquid
                            <div class="liquid-bg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: var(--color-accent); transform: translateY(100%); z-index: -1;"></div>
                        </div>
                    </div>
                `;

                const button = container.querySelector('.demo-button');
                const liquidBg = button.querySelector('.liquid-bg');

                button.addEventListener('mouseenter', () => {
                    gsap.to(liquidBg, {
                        y: '0%',
                        duration: 0.6,
                        ease: 'power2.out'
                    });
                    gsap.to(button, {
                        color: 'white',
                        duration: 0.3
                    });
                });

                button.addEventListener('mouseleave', () => {
                    gsap.to(liquidBg, {
                        y: '100%',
                        duration: 0.4,
                        ease: 'power2.in'
                    });
                    gsap.to(button, {
                        color: 'var(--color-primary)',
                        duration: 0.3
                    });
                });
            }
        }
    ];

    // Register all interactive animations
    interactiveAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
