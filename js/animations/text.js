// Text Animations Module
(function() {
    'use strict';

    const textAnimations = [
        {
            id: 'typewriter-effect',
            title: 'Typewriter Effect',
            description: 'Text appears character by character like typing',
            category: 'text',
            tags: ['typewriter', 'javascript', 'text'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="typewriter-${AnimationUtils.generateId()}">Hello World!</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const text = textElement.textContent;
                textElement.textContent = '';

                // Simple typewriter effect
                let i = 0;
                const typeInterval = setInterval(() => {
                    textElement.textContent += text[i];
                    i++;
                    if (i >= text.length) {
                        clearInterval(typeInterval);
                        setTimeout(() => {
                            textElement.textContent = '';
                            i = 0;
                            const newInterval = setInterval(() => {
                                textElement.textContent += text[i];
                                i++;
                                if (i >= text.length) {
                                    clearInterval(newInterval);
                                }
                            }, 100);
                        }, 2000);
                    }
                }, 100);
            }
        },
        {
            id: 'text-reveal-up',
            title: 'Text Reveal Up',
            description: 'Text reveals from bottom with mask effect',
            category: 'text',
            tags: ['reveal', 'gsap', 'mask', 'splittype'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text demo-text--large" id="reveal-up-${AnimationUtils.generateId()}">REVEAL</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');

                // Create mask effect
                textElement.style.overflow = 'hidden';
                textElement.style.position = 'relative';

                const mask = document.createElement('div');
                mask.style.position = 'absolute';
                mask.style.top = '0';
                mask.style.left = '0';
                mask.style.width = '100%';
                mask.style.height = '100%';
                mask.style.backgroundColor = 'var(--bg-secondary)';
                mask.style.transform = 'translateY(0%)';
                textElement.appendChild(mask);

                // Animate mask
                gsap.to(mask, {
                    y: '-100%',
                    duration: 1,
                    ease: 'power2.out',
                    repeat: -1,
                    repeatDelay: 2,
                    yoyo: true
                });
            }
        },
        {
            id: 'text-stagger-fade',
            title: 'Text Stagger Fade',
            description: 'Letters fade in one by one with stagger effect',
            category: 'text',
            tags: ['stagger', 'gsap', 'fade', 'letters'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="stagger-${AnimationUtils.generateId()}">STAGGER</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const text = textElement.textContent;

                // Split text into spans
                textElement.innerHTML = text.split('').map(char =>
                    char === ' ' ? ' ' : `<span style="opacity: 0;">${char}</span>`
                ).join('');

                const letters = textElement.querySelectorAll('span');

                // Animate letters
                gsap.to(letters, {
                    opacity: 1,
                    duration: 0.5,
                    stagger: 0.1,
                    ease: 'power2.out',
                    repeat: -1,
                    repeatDelay: 2,
                    yoyo: true
                });
            }
        },
        {
            id: 'text-wave',
            title: 'Text Wave',
            description: 'Letters move in a wave-like motion',
            category: 'text',
            tags: ['wave', 'gsap', 'motion', 'letters'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="wave-${AnimationUtils.generateId()}">WAVE</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const text = textElement.textContent;

                // Split text into spans
                textElement.innerHTML = text.split('').map(char =>
                    char === ' ' ? ' ' : `<span style="display: inline-block;">${char}</span>`
                ).join('');

                const letters = textElement.querySelectorAll('span');

                // Create wave animation
                letters.forEach((letter, index) => {
                    gsap.to(letter, {
                        y: -20,
                        duration: 0.5,
                        ease: 'power2.inOut',
                        repeat: -1,
                        yoyo: true,
                        delay: index * 0.1
                    });
                });
            }
        },
        {
            id: 'text-glitch',
            title: 'Text Glitch Effect',
            description: 'Digital glitch effect with random character changes',
            category: 'text',
            tags: ['glitch', 'digital', 'random', 'effect'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="glitch-${AnimationUtils.generateId()}">GLITCH</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const originalText = textElement.textContent;
                const glitchChars = '!@#$%^&*()_+-=[]{}|;:,.<>?';

                function glitchText() {
                    const glitched = originalText.split('').map(char => {
                        if (Math.random() < 0.1) {
                            return glitchChars[Math.floor(Math.random() * glitchChars.length)];
                        }
                        return char;
                    }).join('');

                    textElement.textContent = glitched;

                    setTimeout(() => {
                        textElement.textContent = originalText;
                    }, 100);
                }

                setInterval(glitchText, 2000);
            }
        },
        {
            id: 'text-rainbow',
            title: 'Rainbow Text',
            description: 'Text with animated rainbow gradient effect',
            category: 'text',
            tags: ['rainbow', 'gradient', 'color', 'animation'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text demo-text--large" id="rainbow-${AnimationUtils.generateId()}"
                             style="background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
                                    -webkit-background-clip: text;
                                    -webkit-text-fill-color: transparent;
                                    background-clip: text;
                                    background-size: 400% 400%;
                                    animation: rainbow 3s ease infinite;">RAINBOW</div>
                    </div>
                `;

                // Add rainbow animation keyframes
                if (!document.querySelector('#rainbow-keyframes')) {
                    const style = document.createElement('style');
                    style.id = 'rainbow-keyframes';
                    style.textContent = `
                        @keyframes rainbow {
                            0% { background-position: 0% 50%; }
                            50% { background-position: 100% 50%; }
                            100% { background-position: 0% 50%; }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }
        },
        {
            id: 'text-scramble',
            title: 'Text Scramble',
            description: 'Text scrambles and then resolves to final text',
            category: 'text',
            tags: ['scramble', 'decode', 'random', 'animation'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="scramble-${AnimationUtils.generateId()}">DECODE</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const finalText = textElement.textContent;
                const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

                function scrambleText() {
                    let iterations = 0;
                    const interval = setInterval(() => {
                        textElement.textContent = finalText
                            .split('')
                            .map((char, index) => {
                                if (index < iterations) {
                                    return finalText[index];
                                }
                                return chars[Math.floor(Math.random() * chars.length)];
                            })
                            .join('');

                        if (iterations >= finalText.length) {
                            clearInterval(interval);
                        }

                        iterations += 1 / 3;
                    }, 30);
                }

                scrambleText();
                setInterval(scrambleText, 3000);
            }
        },
        {
            id: 'text-blur-in',
            title: 'Text Blur In',
            description: 'Text comes into focus from blurred state',
            category: 'text',
            tags: ['blur', 'focus', 'filter', 'clarity'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="blur-${AnimationUtils.generateId()}"
                             style="filter: blur(10px); opacity: 0;">FOCUS</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');

                gsap.to(textElement, {
                    filter: 'blur(0px)',
                    opacity: 1,
                    duration: 1.5,
                    ease: 'power2.out',
                    repeat: -1,
                    repeatDelay: 2,
                    yoyo: true
                });
            }
        },
        {
            id: 'text-neon-glow',
            title: 'Neon Glow Text',
            description: 'Text with animated neon glow effect',
            category: 'text',
            tags: ['neon', 'glow', 'shadow', 'electric'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="neon-${AnimationUtils.generateId()}"
                             style="color: #00ffff; text-shadow: 0 0 5px #00ffff;">NEON</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');

                gsap.to(textElement, {
                    textShadow: '0 0 20px #00ffff, 0 0 30px #00ffff, 0 0 40px #00ffff',
                    duration: 1,
                    ease: 'power2.inOut',
                    repeat: -1,
                    yoyo: true
                });
            }
        },
        {
            id: 'text-matrix',
            title: 'Matrix Rain Effect',
            description: 'Digital rain effect like in The Matrix',
            category: 'text',
            tags: ['matrix', 'digital', 'rain', 'code'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden; background: #000;">
                        <div class="demo-text" style="color: #00ff00; position: relative; z-index: 2;">MATRIX</div>
                    </div>
                `;

                const chars = '01アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヲン';

                // Create falling characters
                for (let i = 0; i < 5; i++) {
                    const column = document.createElement('div');
                    column.style.position = 'absolute';
                    column.style.top = '0';
                    column.style.left = (i * 20) + '%';
                    column.style.color = '#00ff00';
                    column.style.fontSize = '12px';
                    column.style.fontFamily = 'monospace';
                    column.style.opacity = '0.3';

                    container.appendChild(column);

                    function dropChar() {
                        const char = document.createElement('div');
                        char.textContent = chars[Math.floor(Math.random() * chars.length)];
                        char.style.position = 'absolute';
                        char.style.top = '-20px';
                        column.appendChild(char);

                        gsap.to(char, {
                            y: 200,
                            opacity: 0,
                            duration: 2,
                            ease: 'none',
                            onComplete: () => {
                                if (char.parentNode) {
                                    char.parentNode.removeChild(char);
                                }
                            }
                        });
                    }

                    setInterval(dropChar, 500 + i * 200);
                }
            }
        },
        {
            id: 'text-bounce-in',
            title: 'Text Bounce In',
            description: 'Letters bounce in one by one with elastic effect',
            category: 'text',
            tags: ['bounce', 'elastic', 'letters', 'entrance'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="bounce-text-${AnimationUtils.generateId()}">BOUNCE</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const text = textElement.textContent;

                // Split text into spans
                textElement.innerHTML = text.split('').map(char =>
                    char === ' ' ? ' ' : `<span style="display: inline-block; transform: scale(0);">${char}</span>`
                ).join('');

                const letters = textElement.querySelectorAll('span');

                // Animate letters
                gsap.to(letters, {
                    scale: 1,
                    duration: 0.6,
                    stagger: 0.1,
                    ease: 'elastic.out(1, 0.3)',
                    repeat: -1,
                    repeatDelay: 3,
                    yoyo: true
                });
            }
        },
        {
            id: 'text-slide-reveal',
            title: 'Text Slide Reveal',
            description: 'Text slides in from behind a mask',
            category: 'text',
            tags: ['slide', 'reveal', 'mask', 'entrance'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div style="overflow: hidden; position: relative;">
                            <div class="demo-text" id="slide-reveal-${AnimationUtils.generateId()}"
                                 style="transform: translateX(-100%);">SLIDE</div>
                        </div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');

                gsap.to(textElement, {
                    x: '0%',
                    duration: 0.8,
                    ease: 'power2.out',
                    repeat: -1,
                    repeatDelay: 2,
                    yoyo: true
                });
            }
        }
    ];

    // Register all text animations
    textAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
