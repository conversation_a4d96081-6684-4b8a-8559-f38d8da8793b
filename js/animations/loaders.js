// Loader Animations Module
(function() {
    'use strict';

    const loaderAnimations = [
        {
            id: 'spinning-loader',
            title: 'Spinning Loader',
            description: 'Classic spinning circle loader animation',
            category: 'loaders',
            tags: ['loader', 'spinner', 'css', 'rotation'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-loader">
                            <div class="demo-loader__spinner"></div>
                        </div>
                    </div>
                `;
            }
        },
        {
            id: 'dots-loader',
            title: 'Bouncing Dots',
            description: 'Three dots bouncing in sequence',
            category: 'loaders',
            tags: ['loader', 'dots', 'bounce', 'sequence'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-flex">
                            <div class="demo-shape demo-shape--small demo-shape--circle" style="animation: bounce 1.4s ease-in-out infinite both; animation-delay: -0.32s;"></div>
                            <div class="demo-shape demo-shape--small demo-shape--circle" style="animation: bounce 1.4s ease-in-out infinite both; animation-delay: -0.16s;"></div>
                            <div class="demo-shape demo-shape--small demo-shape--circle" style="animation: bounce 1.4s ease-in-out infinite both;"></div>
                        </div>
                    </div>
                `;
            }
        },
        {
            id: 'pulse-loader',
            title: 'Pulse Loader',
            description: 'Expanding and fading pulse rings',
            category: 'loaders',
            tags: ['loader', 'pulse', 'rings', 'expand'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative;">
                        <div id="pulse-container-${AnimationUtils.generateId()}" style="position: relative; width: 60px; height: 60px;"></div>
                    </div>
                `;

                const pulseContainer = container.querySelector('[id^="pulse-container"]');

                // Create multiple pulse rings
                for (let i = 0; i < 3; i++) {
                    const ring = document.createElement('div');
                    ring.style.position = 'absolute';
                    ring.style.top = '50%';
                    ring.style.left = '50%';
                    ring.style.width = '20px';
                    ring.style.height = '20px';
                    ring.style.border = '2px solid var(--color-primary)';
                    ring.style.borderRadius = '50%';
                    ring.style.transform = 'translate(-50%, -50%)';
                    ring.style.opacity = '0';

                    pulseContainer.appendChild(ring);

                    gsap.to(ring, {
                        scale: 3,
                        opacity: 0,
                        duration: 2,
                        ease: 'power2.out',
                        repeat: -1,
                        delay: i * 0.6
                    });
                }
            }
        },
        {
            id: 'bars-loader',
            title: 'Dancing Bars',
            description: 'Vertical bars dancing up and down',
            category: 'loaders',
            tags: ['loader', 'bars', 'dance', 'vertical'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-flex" style="align-items: flex-end; height: 60px;">
                            <div class="bar" style="width: 4px; background: var(--color-primary); margin: 0 2px;"></div>
                            <div class="bar" style="width: 4px; background: var(--color-primary); margin: 0 2px;"></div>
                            <div class="bar" style="width: 4px; background: var(--color-primary); margin: 0 2px;"></div>
                            <div class="bar" style="width: 4px; background: var(--color-primary); margin: 0 2px;"></div>
                            <div class="bar" style="width: 4px; background: var(--color-primary); margin: 0 2px;"></div>
                        </div>
                    </div>
                `;

                const bars = container.querySelectorAll('.bar');

                bars.forEach((bar, index) => {
                    gsap.to(bar, {
                        height: () => AnimationUtils.randomBetween(10, 50) + 'px',
                        duration: 0.5,
                        ease: 'power2.inOut',
                        repeat: -1,
                        yoyo: true,
                        delay: index * 0.1
                    });
                });
            }
        },
        {
            id: 'orbit-loader',
            title: 'Orbit Loader',
            description: 'Dots orbiting around a center point',
            category: 'loaders',
            tags: ['loader', 'orbit', 'circular', 'rotation'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative;">
                        <div id="orbit-container-${AnimationUtils.generateId()}" style="position: relative; width: 80px; height: 80px;"></div>
                    </div>
                `;

                const orbitContainer = container.querySelector('[id^="orbit-container"]');

                // Create orbiting dots
                for (let i = 0; i < 6; i++) {
                    const dot = document.createElement('div');
                    dot.style.position = 'absolute';
                    dot.style.width = '8px';
                    dot.style.height = '8px';
                    dot.style.backgroundColor = AnimationUtils.randomColor();
                    dot.style.borderRadius = '50%';
                    dot.style.top = '50%';
                    dot.style.left = '50%';
                    dot.style.transform = 'translate(-50%, -50%)';

                    orbitContainer.appendChild(dot);

                    const angle = (i / 6) * 360;
                    const radius = 30;

                    gsap.set(dot, {
                        x: Math.cos(angle * Math.PI / 180) * radius,
                        y: Math.sin(angle * Math.PI / 180) * radius
                    });

                    gsap.to(dot, {
                        rotation: 360,
                        duration: 2,
                        ease: 'none',
                        repeat: -1,
                        transformOrigin: `${-Math.cos(angle * Math.PI / 180) * radius}px ${-Math.sin(angle * Math.PI / 180) * radius}px`
                    });
                }
            }
        },
        {
            id: 'wave-loader',
            title: 'Wave Loader',
            description: 'Sine wave animation with flowing motion',
            category: 'loaders',
            tags: ['loader', 'wave', 'sine', 'flow'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-flex" style="align-items: center;">
                            <div class="wave-dot" style="width: 8px; height: 8px; background: var(--color-primary); border-radius: 50%; margin: 0 2px;"></div>
                            <div class="wave-dot" style="width: 8px; height: 8px; background: var(--color-primary); border-radius: 50%; margin: 0 2px;"></div>
                            <div class="wave-dot" style="width: 8px; height: 8px; background: var(--color-primary); border-radius: 50%; margin: 0 2px;"></div>
                            <div class="wave-dot" style="width: 8px; height: 8px; background: var(--color-primary); border-radius: 50%; margin: 0 2px;"></div>
                            <div class="wave-dot" style="width: 8px; height: 8px; background: var(--color-primary); border-radius: 50%; margin: 0 2px;"></div>
                            <div class="wave-dot" style="width: 8px; height: 8px; background: var(--color-primary); border-radius: 50%; margin: 0 2px;"></div>
                        </div>
                    </div>
                `;

                const dots = container.querySelectorAll('.wave-dot');

                dots.forEach((dot, index) => {
                    gsap.to(dot, {
                        y: -15,
                        duration: 0.6,
                        ease: 'power2.inOut',
                        repeat: -1,
                        yoyo: true,
                        delay: index * 0.1
                    });
                });
            }
        },
        {
            id: 'progress-bar',
            title: 'Progress Bar',
            description: 'Animated progress bar with percentage',
            category: 'loaders',
            tags: ['loader', 'progress', 'bar', 'percentage'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="width: 100%; max-width: 200px;">
                        <div class="demo-progress">
                            <div class="demo-progress__bar" id="progress-${AnimationUtils.generateId()}" style="width: 0%;"></div>
                        </div>
                        <div class="progress-text" style="text-align: center; margin-top: 8px; font-size: 12px;">0%</div>
                    </div>
                `;

                const progressBar = container.querySelector('.demo-progress__bar');
                const progressText = container.querySelector('.progress-text');

                gsap.to(progressBar, {
                    width: '100%',
                    duration: 3,
                    ease: 'power2.out',
                    repeat: -1,
                    repeatDelay: 1,
                    onUpdate: function() {
                        const progress = Math.round(this.progress() * 100);
                        progressText.textContent = progress + '%';
                    },
                    onRepeat: function() {
                        progressText.textContent = '0%';
                    }
                });
            }
        },
        {
            id: 'skeleton-loader',
            title: 'Skeleton Loader',
            description: 'Shimmer effect skeleton loading animation',
            category: 'loaders',
            tags: ['loader', 'skeleton', 'shimmer', 'placeholder'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="width: 100%; max-width: 200px;">
                        <div class="skeleton-item" style="height: 20px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; border-radius: 4px; margin-bottom: 8px;"></div>
                        <div class="skeleton-item" style="height: 16px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; border-radius: 4px; margin-bottom: 8px; width: 80%;"></div>
                        <div class="skeleton-item" style="height: 16px; background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; border-radius: 4px; width: 60%;"></div>
                    </div>
                `;

                const skeletonItems = container.querySelectorAll('.skeleton-item');

                skeletonItems.forEach(item => {
                    gsap.to(item, {
                        backgroundPosition: '200% 0',
                        duration: 1.5,
                        ease: 'none',
                        repeat: -1
                    });
                });
            }
        }
    ];

    // Register all loader animations
    loaderAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
