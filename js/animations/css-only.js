// CSS-Only Animations Module
(function() {
    'use strict';
    
    const cssOnlyAnimations = [
        {
            id: 'hover-grow',
            title: 'Hover Grow',
            description: 'Element scales up on hover with smooth transition',
            category: 'css-only',
            tags: ['hover', 'transform', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-grow">Hover Me</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-shrink',
            title: 'Hover Shrink',
            description: 'Element scales down on hover with smooth transition',
            category: 'css-only',
            tags: ['hover', 'transform', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-shrink">Hover Me</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-rotate',
            title: 'Hover Rotate',
            description: 'Element rotates on hover with smooth transition',
            category: 'css-only',
            tags: ['hover', 'transform', 'rotate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape hover-rotate"></div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-skew',
            title: 'Hover Skew',
            description: 'Element skews on hover creating a slanted effect',
            category: 'css-only',
            tags: ['hover', 'transform', 'skew'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-skew">Skew</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-float',
            title: 'Hover Float',
            description: 'Element floats up on hover with smooth transition',
            category: 'css-only',
            tags: ['hover', 'transform', 'translate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-float">Float</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-sink',
            title: 'Hover Sink',
            description: 'Element sinks down on hover with smooth transition',
            category: 'css-only',
            tags: ['hover', 'transform', 'translate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-sink">Sink</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-shadow',
            title: 'Hover Shadow',
            description: 'Element gains a dramatic shadow on hover',
            category: 'css-only',
            tags: ['hover', 'box-shadow', 'depth'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-shadow">Shadow</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-glow',
            title: 'Hover Glow',
            description: 'Element glows with colored shadow on hover',
            category: 'css-only',
            tags: ['hover', 'box-shadow', 'glow'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-glow">Glow</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-border-radius',
            title: 'Hover Border Radius',
            description: 'Element border radius changes on hover',
            category: 'css-only',
            tags: ['hover', 'border-radius', 'morph'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape hover-border-radius"></div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-color-change',
            title: 'Hover Color Change',
            description: 'Element changes background and text color on hover',
            category: 'css-only',
            tags: ['hover', 'color', 'background'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-color-change">Color Change</div>
                    </div>
                `;
            }
        },
        {
            id: 'pulse-animation',
            title: 'Pulse Animation',
            description: 'Continuous pulsing animation with scale and opacity',
            category: 'css-only',
            tags: ['keyframes', 'pulse', 'infinite'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape demo-shape--circle pulse"></div>
                    </div>
                `;
            }
        },
        {
            id: 'bounce-animation',
            title: 'Bounce Animation',
            description: 'Bouncing ball effect with realistic physics',
            category: 'css-only',
            tags: ['keyframes', 'bounce', 'physics'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape demo-shape--circle bounce"></div>
                    </div>
                `;
            }
        },
        {
            id: 'shake-animation',
            title: 'Shake Animation',
            description: 'Rapid horizontal shaking motion',
            category: 'css-only',
            tags: ['keyframes', 'shake', 'attention'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button shake">Shake</div>
                    </div>
                `;
            }
        },
        {
            id: 'wobble-animation',
            title: 'Wobble Animation',
            description: 'Wobbling motion with rotation and translation',
            category: 'css-only',
            tags: ['keyframes', 'wobble', 'complex'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card wobble">Wobble</div>
                    </div>
                `;
            }
        },
        {
            id: 'swing-animation',
            title: 'Swing Animation',
            description: 'Pendulum-like swinging motion',
            category: 'css-only',
            tags: ['keyframes', 'swing', 'rotation'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card swing">Swing</div>
                    </div>
                `;
            }
        },
        {
            id: 'fade-in',
            title: 'Fade In',
            description: 'Simple fade in animation from transparent to opaque',
            category: 'css-only',
            tags: ['keyframes', 'fade', 'opacity'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text fade-in">Fade In</div>
                    </div>
                `;
                
                // Restart animation on click
                const element = container.querySelector('.fade-in');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight; // Trigger reflow
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'fade-in-up',
            title: 'Fade In Up',
            description: 'Fade in animation combined with upward movement',
            category: 'css-only',
            tags: ['keyframes', 'fade', 'translate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text fade-in-up">Fade In Up</div>
                    </div>
                `;
                
                const element = container.querySelector('.fade-in-up');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'fade-in-down',
            title: 'Fade In Down',
            description: 'Fade in animation combined with downward movement',
            category: 'css-only',
            tags: ['keyframes', 'fade', 'translate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text fade-in-down">Fade In Down</div>
                    </div>
                `;
                
                const element = container.querySelector('.fade-in-down');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'fade-in-left',
            title: 'Fade In Left',
            description: 'Fade in animation combined with leftward movement',
            category: 'css-only',
            tags: ['keyframes', 'fade', 'translate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text fade-in-left">Fade In Left</div>
                    </div>
                `;
                
                const element = container.querySelector('.fade-in-left');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'fade-in-right',
            title: 'Fade In Right',
            description: 'Fade in animation combined with rightward movement',
            category: 'css-only',
            tags: ['keyframes', 'fade', 'translate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text fade-in-right">Fade In Right</div>
                    </div>
                `;
                
                const element = container.querySelector('.fade-in-right');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'zoom-in',
            title: 'Zoom In',
            description: 'Element zooms in from small to normal size',
            category: 'css-only',
            tags: ['keyframes', 'zoom', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape zoom-in"></div>
                    </div>
                `;
                
                const element = container.querySelector('.zoom-in');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'flip-in-x',
            title: 'Flip In X',
            description: '3D flip animation along the X-axis',
            category: 'css-only',
            tags: ['keyframes', 'flip', '3d', 'perspective'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card flip-in-x">Flip X</div>
                    </div>
                `;
                
                const element = container.querySelector('.flip-in-x');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'flip-in-y',
            title: 'Flip In Y',
            description: '3D flip animation along the Y-axis',
            category: 'css-only',
            tags: ['keyframes', 'flip', '3d', 'perspective'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card flip-in-y">Flip Y</div>
                    </div>
                `;
                
                const element = container.querySelector('.flip-in-y');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'rotate-in',
            title: 'Rotate In',
            description: 'Element rotates in from a tilted position',
            category: 'css-only',
            tags: ['keyframes', 'rotate', 'entrance'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape rotate-in"></div>
                    </div>
                `;
                
                const element = container.querySelector('.rotate-in');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'hover-slide',
            title: 'Hover Slide Effect',
            description: 'Sliding shine effect on hover',
            category: 'css-only',
            tags: ['hover', 'slide', 'shine', 'pseudo-element'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-slide">Slide Effect</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-underline',
            title: 'Hover Underline',
            description: 'Animated underline that expands on hover',
            category: 'css-only',
            tags: ['hover', 'underline', 'text', 'pseudo-element'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text hover-underline">Hover Underline</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-fill',
            title: 'Hover Fill',
            description: 'Background fills from left to right on hover',
            category: 'css-only',
            tags: ['hover', 'fill', 'background', 'pseudo-element'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-fill">Fill Effect</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-tilt',
            title: 'Hover 3D Tilt',
            description: '3D tilt effect using perspective on hover',
            category: 'css-only',
            tags: ['hover', '3d', 'tilt', 'perspective'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-tilt">3D Tilt</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-flip',
            title: 'Hover Flip',
            description: 'Element flips 180 degrees on hover',
            category: 'css-only',
            tags: ['hover', 'flip', '3d', 'rotate'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-flip">Flip</div>
                    </div>
                `;
            }
        },
        {
            id: 'heartbeat-animation',
            title: 'Heartbeat Animation',
            description: 'Pulsing heartbeat-like animation',
            category: 'css-only',
            tags: ['keyframes', 'heartbeat', 'pulse', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape demo-shape--circle heartbeat" style="background-color: #ef4444;"></div>
                    </div>
                `;
            }
        },
        {
            id: 'flash-animation',
            title: 'Flash Animation',
            description: 'Blinking flash effect with opacity changes',
            category: 'css-only',
            tags: ['keyframes', 'flash', 'blink', 'opacity'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text flash">Flash!</div>
                    </div>
                `;
            }
        },
        {
            id: 'rubber-band-animation',
            title: 'Rubber Band',
            description: 'Elastic rubber band stretching effect',
            category: 'css-only',
            tags: ['keyframes', 'rubber-band', 'elastic', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape rubber-band"></div>
                    </div>
                `;

                const element = container.querySelector('.rubber-band');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'jello-animation',
            title: 'Jello Animation',
            description: 'Jello-like wobbling and skewing effect',
            category: 'css-only',
            tags: ['keyframes', 'jello', 'skew', 'wobble'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card jello">Jello</div>
                    </div>
                `;

                const element = container.querySelector('.jello');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'tada-animation',
            title: 'Tada Animation',
            description: 'Celebratory tada effect with scale and rotation',
            category: 'css-only',
            tags: ['keyframes', 'tada', 'celebration', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card tada">Tada!</div>
                    </div>
                `;

                const element = container.querySelector('.tada');
                element.addEventListener('click', () => {
                    element.style.animation = 'none';
                    element.offsetHeight;
                    element.style.animation = null;
                });
            }
        },
        {
            id: 'hover-buzz',
            title: 'Hover Buzz',
            description: 'Rapid buzzing vibration effect on hover',
            category: 'css-only',
            tags: ['hover', 'buzz', 'vibrate', 'shake'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-button hover-buzz">Buzz</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-forward',
            title: 'Hover Forward',
            description: 'Element moves forward (right) on hover',
            category: 'css-only',
            tags: ['hover', 'forward', 'translate', 'direction'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-forward">Forward</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-backward',
            title: 'Hover Backward',
            description: 'Element moves backward (left) on hover',
            category: 'css-only',
            tags: ['hover', 'backward', 'translate', 'direction'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-backward">Backward</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-bob',
            title: 'Hover Bob',
            description: 'Gentle bobbing motion on hover',
            category: 'css-only',
            tags: ['hover', 'bob', 'float', 'gentle'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape hover-bob">Bob</div>
                    </div>
                `;
            }
        },
        {
            id: 'hover-hang',
            title: 'Hover Hang',
            description: 'Hanging pendulum motion on hover',
            category: 'css-only',
            tags: ['hover', 'hang', 'pendulum', 'swing'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card hover-hang">Hang</div>
                    </div>
                `;
            }
        }
    ];

    // Register all CSS-only animations
    cssOnlyAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
