// Scroll-Based Animations Module
(function() {
    'use strict';

    const scrollAnimations = [
        {
            id: 'scroll-fade-in',
            title: 'Scroll Fade In',
            description: 'Element fades in when scrolled into view',
            category: 'scroll',
            tags: ['scroll', 'fade', 'gsap', 'scrolltrigger'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text">Hover to simulate scroll</div>
                    </div>
                `;

                // Simulate scroll trigger with hover
                const element = container.querySelector('.demo-text');
                element.style.opacity = '0.3';

                container.addEventListener('mouseenter', () => {
                    gsap.to(element, { opacity: 1, duration: 0.5 });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(element, { opacity: 0.3, duration: 0.5 });
                });
            }
        },
        {
            id: 'scroll-slide-up',
            title: 'Scroll Slide Up',
            description: 'Element slides up from bottom on scroll',
            category: 'scroll',
            tags: ['scroll', 'slide', 'transform', 'reveal'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card">Slide Up</div>
                    </div>
                `;

                const element = container.querySelector('.demo-card');
                gsap.set(element, { y: 50, opacity: 0 });

                container.addEventListener('mouseenter', () => {
                    gsap.to(element, { y: 0, opacity: 1, duration: 0.6, ease: 'power2.out' });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(element, { y: 50, opacity: 0, duration: 0.4, ease: 'power2.in' });
                });
            }
        },
        {
            id: 'scroll-scale-in',
            title: 'Scroll Scale In',
            description: 'Element scales in from small to normal size',
            category: 'scroll',
            tags: ['scroll', 'scale', 'zoom', 'entrance'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape">Scale</div>
                    </div>
                `;

                const element = container.querySelector('.demo-shape');
                gsap.set(element, { scale: 0.5, opacity: 0 });

                container.addEventListener('mouseenter', () => {
                    gsap.to(element, { scale: 1, opacity: 1, duration: 0.6, ease: 'back.out(1.7)' });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(element, { scale: 0.5, opacity: 0, duration: 0.4, ease: 'power2.in' });
                });
            }
        },
        {
            id: 'scroll-rotate-in',
            title: 'Scroll Rotate In',
            description: 'Element rotates in while fading',
            category: 'scroll',
            tags: ['scroll', 'rotate', 'fade', 'entrance'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape">Rotate</div>
                    </div>
                `;

                const element = container.querySelector('.demo-shape');
                gsap.set(element, { rotation: -180, opacity: 0 });

                container.addEventListener('mouseenter', () => {
                    gsap.to(element, { rotation: 0, opacity: 1, duration: 0.8, ease: 'power2.out' });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(element, { rotation: -180, opacity: 0, duration: 0.4, ease: 'power2.in' });
                });
            }
        },
        {
            id: 'parallax-background',
            title: 'Parallax Background',
            description: 'Background moves at different speed creating depth',
            category: 'scroll',
            tags: ['parallax', 'background', 'depth', 'layers'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden;">
                        <div class="bg-layer" style="position: absolute; top: 0; left: 0; width: 120%; height: 120%; background: linear-gradient(45deg, var(--color-primary), var(--color-accent)); opacity: 0.3;"></div>
                        <div class="demo-text" style="position: relative; z-index: 1;">Parallax</div>
                    </div>
                `;

                const bgLayer = container.querySelector('.bg-layer');

                container.addEventListener('mousemove', (e) => {
                    const rect = container.getBoundingClientRect();
                    const x = (e.clientX - rect.left) / rect.width;
                    const y = (e.clientY - rect.top) / rect.height;

                    gsap.to(bgLayer, {
                        x: (x - 0.5) * 20,
                        y: (y - 0.5) * 20,
                        duration: 0.3,
                        ease: 'power2.out'
                    });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(bgLayer, {
                        x: 0,
                        y: 0,
                        duration: 0.5,
                        ease: 'power2.out'
                    });
                });
            }
        },
        {
            id: 'scroll-counter',
            title: 'Scroll Counter',
            description: 'Numbers count up when element comes into view',
            category: 'scroll',
            tags: ['scroll', 'counter', 'numbers', 'animation'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text demo-text--large" id="counter-${AnimationUtils.generateId()}">0</div>
                    </div>
                `;

                const counter = container.querySelector('.demo-text');
                let isAnimating = false;

                container.addEventListener('mouseenter', () => {
                    if (!isAnimating) {
                        isAnimating = true;
                        gsap.to({ value: 0 }, {
                            value: 100,
                            duration: 2,
                            ease: 'power2.out',
                            onUpdate: function() {
                                counter.textContent = Math.round(this.targets()[0].value);
                            },
                            onComplete: () => {
                                setTimeout(() => {
                                    isAnimating = false;
                                }, 1000);
                            }
                        });
                    }
                });

                container.addEventListener('mouseleave', () => {
                    if (!isAnimating) {
                        gsap.to({ value: parseInt(counter.textContent) }, {
                            value: 0,
                            duration: 0.5,
                            ease: 'power2.in',
                            onUpdate: function() {
                                counter.textContent = Math.round(this.targets()[0].value);
                            }
                        });
                    }
                });
            }
        },
        {
            id: 'scroll-progress',
            title: 'Scroll Progress',
            description: 'Progress bar fills based on scroll position',
            category: 'scroll',
            tags: ['scroll', 'progress', 'indicator', 'position'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="width: 100%; max-width: 200px;">
                        <div class="demo-progress">
                            <div class="demo-progress__bar" id="scroll-progress-${AnimationUtils.generateId()}" style="width: 0%;"></div>
                        </div>
                        <div style="text-align: center; margin-top: 8px; font-size: 12px;">Hover to simulate scroll</div>
                    </div>
                `;

                const progressBar = container.querySelector('.demo-progress__bar');

                container.addEventListener('mouseenter', () => {
                    gsap.to(progressBar, {
                        width: '100%',
                        duration: 1,
                        ease: 'power2.out'
                    });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(progressBar, {
                        width: '0%',
                        duration: 0.5,
                        ease: 'power2.in'
                    });
                });
            }
        },
        {
            id: 'scroll-reveal-mask',
            title: 'Scroll Reveal Mask',
            description: 'Content reveals through an animated mask',
            category: 'scroll',
            tags: ['scroll', 'reveal', 'mask', 'clip-path'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="mask-reveal-${AnimationUtils.generateId()}"
                             style="clip-path: inset(0 100% 0 0);">REVEAL</div>
                    </div>
                `;

                const element = container.querySelector('.demo-text');

                container.addEventListener('mouseenter', () => {
                    gsap.to(element, {
                        clipPath: 'inset(0 0% 0 0)',
                        duration: 0.8,
                        ease: 'power2.out'
                    });
                });

                container.addEventListener('mouseleave', () => {
                    gsap.to(element, {
                        clipPath: 'inset(0 100% 0 0)',
                        duration: 0.5,
                        ease: 'power2.in'
                    });
                });
            }
        }
    ];

    // Register all scroll animations
    scrollAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
