// Page Transition Animations Module
(function() {
    'use strict';

    const transitionAnimations = [
        {
            id: 'slide-transition',
            title: 'Slide Transition',
            description: 'Smooth sliding transition effect',
            category: 'transitions',
            tags: ['transition', 'slide', 'gsap', 'page'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card" id="slide-${AnimationUtils.generateId()}">Click to slide</div>
                    </div>
                `;

                const card = container.querySelector('.demo-card');
                let isSlid = false;

                card.addEventListener('click', () => {
                    if (!isSlid) {
                        gsap.to(card, {
                            x: 50,
                            duration: 0.5,
                            ease: "power2.out"
                        });
                        isSlid = true;
                    } else {
                        gsap.to(card, {
                            x: 0,
                            duration: 0.5,
                            ease: "power2.out"
                        });
                        isSlid = false;
                    }
                });
            }
        },
        {
            id: 'fade-transition',
            title: 'Fade Transition',
            description: 'Cross-fade between two states',
            category: 'transitions',
            tags: ['transition', 'fade', 'opacity', 'cross-fade'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative;">
                        <div class="demo-card state-a" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">State A</div>
                        <div class="demo-card state-b" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 0; background-color: var(--color-accent);">State B</div>
                    </div>
                `;

                const stateA = container.querySelector('.state-a');
                const stateB = container.querySelector('.state-b');
                let currentState = 'A';

                container.addEventListener('click', () => {
                    if (currentState === 'A') {
                        gsap.to(stateA, { opacity: 0, duration: 0.3 });
                        gsap.to(stateB, { opacity: 1, duration: 0.3 });
                        currentState = 'B';
                    } else {
                        gsap.to(stateA, { opacity: 1, duration: 0.3 });
                        gsap.to(stateB, { opacity: 0, duration: 0.3 });
                        currentState = 'A';
                    }
                });
            }
        },
        {
            id: 'scale-transition',
            title: 'Scale Transition',
            description: 'Element scales out and new one scales in',
            category: 'transitions',
            tags: ['transition', 'scale', 'zoom', 'replace'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape" id="scale-shape-${AnimationUtils.generateId()}"></div>
                    </div>
                `;

                const shape = container.querySelector('.demo-shape');
                const colors = ['var(--color-primary)', 'var(--color-accent)', 'var(--color-success)', 'var(--color-warning)'];
                let colorIndex = 0;

                shape.addEventListener('click', () => {
                    gsap.to(shape, {
                        scale: 0,
                        duration: 0.2,
                        ease: 'power2.in',
                        onComplete: () => {
                            colorIndex = (colorIndex + 1) % colors.length;
                            shape.style.backgroundColor = colors[colorIndex];
                            gsap.to(shape, {
                                scale: 1,
                                duration: 0.3,
                                ease: 'back.out(1.7)'
                            });
                        }
                    });
                });
            }
        },
        {
            id: 'flip-transition',
            title: 'Flip Transition',
            description: '3D flip transition between front and back',
            category: 'transitions',
            tags: ['transition', 'flip', '3d', 'card'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="flip-container" style="perspective: 1000px; cursor: pointer;">
                            <div class="flip-card" style="position: relative; width: 100px; height: 60px; transform-style: preserve-3d; transition: transform 0.6s;">
                                <div class="flip-front" style="position: absolute; width: 100%; height: 100%; backface-visibility: hidden; background: var(--color-primary); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">Front</div>
                                <div class="flip-back" style="position: absolute; width: 100%; height: 100%; backface-visibility: hidden; background: var(--color-accent); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px; transform: rotateY(180deg);">Back</div>
                            </div>
                        </div>
                    </div>
                `;

                const flipContainer = container.querySelector('.flip-container');
                const flipCard = container.querySelector('.flip-card');
                let isFlipped = false;

                flipContainer.addEventListener('click', () => {
                    if (!isFlipped) {
                        flipCard.style.transform = 'rotateY(180deg)';
                        isFlipped = true;
                    } else {
                        flipCard.style.transform = 'rotateY(0deg)';
                        isFlipped = false;
                    }
                });
            }
        },
        {
            id: 'wipe-transition',
            title: 'Wipe Transition',
            description: 'Content wipes in from one side',
            category: 'transitions',
            tags: ['transition', 'wipe', 'reveal', 'mask'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden;">
                        <div class="demo-card wipe-content" style="clip-path: inset(0 100% 0 0);">Wiped In</div>
                    </div>
                `;

                const content = container.querySelector('.wipe-content');
                let isWiped = false;

                container.addEventListener('click', () => {
                    if (!isWiped) {
                        gsap.to(content, {
                            clipPath: 'inset(0 0% 0 0)',
                            duration: 0.6,
                            ease: 'power2.out'
                        });
                        isWiped = true;
                    } else {
                        gsap.to(content, {
                            clipPath: 'inset(0 100% 0 0)',
                            duration: 0.4,
                            ease: 'power2.in'
                        });
                        isWiped = false;
                    }
                });
            }
        },
        {
            id: 'curtain-transition',
            title: 'Curtain Transition',
            description: 'Curtain-like reveal from center outward',
            category: 'transitions',
            tags: ['transition', 'curtain', 'reveal', 'center'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden;">
                        <div class="demo-card curtain-content" style="clip-path: inset(0 50% 0 50%);">Curtain</div>
                    </div>
                `;

                const content = container.querySelector('.curtain-content');
                let isOpen = false;

                container.addEventListener('click', () => {
                    if (!isOpen) {
                        gsap.to(content, {
                            clipPath: 'inset(0 0% 0 0%)',
                            duration: 0.8,
                            ease: 'power2.out'
                        });
                        isOpen = true;
                    } else {
                        gsap.to(content, {
                            clipPath: 'inset(0 50% 0 50%)',
                            duration: 0.6,
                            ease: 'power2.in'
                        });
                        isOpen = false;
                    }
                });
            }
        },
        {
            id: 'zoom-transition',
            title: 'Zoom Transition',
            description: 'Content zooms in from center point',
            category: 'transitions',
            tags: ['transition', 'zoom', 'scale', 'center'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card zoom-content" style="transform: scale(0); opacity: 0;">Zoom In</div>
                    </div>
                `;

                const content = container.querySelector('.zoom-content');
                let isZoomed = false;

                container.addEventListener('click', () => {
                    if (!isZoomed) {
                        gsap.to(content, {
                            scale: 1,
                            opacity: 1,
                            duration: 0.5,
                            ease: 'back.out(1.7)'
                        });
                        isZoomed = true;
                    } else {
                        gsap.to(content, {
                            scale: 0,
                            opacity: 0,
                            duration: 0.3,
                            ease: 'power2.in'
                        });
                        isZoomed = false;
                    }
                });
            }
        },
        {
            id: 'spiral-transition',
            title: 'Spiral Transition',
            description: 'Content spirals in with rotation and scale',
            category: 'transitions',
            tags: ['transition', 'spiral', 'rotation', 'scale'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape spiral-content" style="transform: scale(0) rotate(-360deg); opacity: 0;"></div>
                    </div>
                `;

                const content = container.querySelector('.spiral-content');
                let isSpiraled = false;

                container.addEventListener('click', () => {
                    if (!isSpiraled) {
                        gsap.to(content, {
                            scale: 1,
                            rotation: 0,
                            opacity: 1,
                            duration: 0.8,
                            ease: 'power2.out'
                        });
                        isSpiraled = true;
                    } else {
                        gsap.to(content, {
                            scale: 0,
                            rotation: -360,
                            opacity: 0,
                            duration: 0.5,
                            ease: 'power2.in'
                        });
                        isSpiraled = false;
                    }
                });
            }
        },
        {
            id: 'elastic-transition',
            title: 'Elastic Transition',
            description: 'Bouncy elastic entrance and exit',
            category: 'transitions',
            tags: ['transition', 'elastic', 'bounce', 'spring'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-card elastic-content" style="transform: scale(0); opacity: 0;">Elastic</div>
                    </div>
                `;

                const content = container.querySelector('.elastic-content');
                let isVisible = false;

                container.addEventListener('click', () => {
                    if (!isVisible) {
                        gsap.to(content, {
                            scale: 1,
                            opacity: 1,
                            duration: 0.8,
                            ease: 'elastic.out(1, 0.3)'
                        });
                        isVisible = true;
                    } else {
                        gsap.to(content, {
                            scale: 0,
                            opacity: 0,
                            duration: 0.4,
                            ease: 'power2.in'
                        });
                        isVisible = false;
                    }
                });
            }
        },
        {
            id: 'morph-transition',
            title: 'Morph Transition',
            description: 'Shape morphs while transitioning',
            category: 'transitions',
            tags: ['transition', 'morph', 'shape', 'transform'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape morph-content" style="border-radius: 0%;"></div>
                    </div>
                `;

                const content = container.querySelector('.morph-content');
                let isMorphed = false;

                container.addEventListener('click', () => {
                    if (!isMorphed) {
                        gsap.to(content, {
                            borderRadius: '50%',
                            scale: 1.2,
                            backgroundColor: 'var(--color-accent)',
                            rotation: 180,
                            duration: 0.6,
                            ease: 'power2.inOut'
                        });
                        isMorphed = true;
                    } else {
                        gsap.to(content, {
                            borderRadius: '0%',
                            scale: 1,
                            backgroundColor: 'var(--color-primary)',
                            rotation: 0,
                            duration: 0.6,
                            ease: 'power2.inOut'
                        });
                        isMorphed = false;
                    }
                });
            }
        }
    ];

    // Register all transition animations
    transitionAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
