// GSAP Advanced Animations Module
(function() {
    'use strict';

    const gsapAnimations = [
        {
            id: 'morphing-shape',
            title: 'Morphing Shape',
            description: 'Shape morphs between different forms using GSAP',
            category: 'gsap',
            tags: ['morph', 'gsap', 'timeline', 'shape'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape" id="morph-${AnimationUtils.generateId()}"></div>
                    </div>
                `;

                const shape = container.querySelector('.demo-shape');

                const tl = gsap.timeline({ repeat: -1, yoyo: true });
                tl.to(shape, {
                    borderRadius: "50%",
                    rotation: 180,
                    scale: 1.2,
                    backgroundColor: "#ec4899",
                    duration: 1,
                    ease: "power2.inOut"
                })
                .to(shape, {
                    borderRadius: "0%",
                    rotation: 360,
                    scale: 0.8,
                    backgroundColor: "#10b981",
                    duration: 1,
                    ease: "power2.inOut"
                });
            }
        },
        {
            id: 'stagger-boxes',
            title: 'Stagger Animation',
            description: 'Multiple elements animate with staggered timing',
            category: 'gsap',
            tags: ['stagger', 'gsap', 'multiple', 'timing'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-grid">
                            <div class="demo-shape demo-shape--small"></div>
                            <div class="demo-shape demo-shape--small"></div>
                            <div class="demo-shape demo-shape--small"></div>
                            <div class="demo-shape demo-shape--small"></div>
                            <div class="demo-shape demo-shape--small"></div>
                            <div class="demo-shape demo-shape--small"></div>
                        </div>
                    </div>
                `;

                const boxes = container.querySelectorAll('.demo-shape');

                gsap.to(boxes, {
                    y: -20,
                    scale: 1.2,
                    backgroundColor: "#f59e0b",
                    duration: 0.5,
                    stagger: 0.1,
                    ease: "power2.out",
                    repeat: -1,
                    yoyo: true
                });
            }
        },
        {
            id: 'physics-bounce',
            title: 'Physics Bounce',
            description: 'Realistic physics-based bouncing animation',
            category: 'gsap',
            tags: ['physics', 'bounce', 'realistic', 'gravity'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; height: 200px;">
                        <div class="demo-shape demo-shape--circle" id="bounce-${AnimationUtils.generateId()}"
                             style="position: absolute; top: 20px; left: 50%; transform: translateX(-50%);"></div>
                    </div>
                `;

                const ball = container.querySelector('.demo-shape');

                const tl = gsap.timeline({ repeat: -1 });
                tl.to(ball, {
                    y: 120,
                    duration: 0.5,
                    ease: "power2.in"
                })
                .to(ball, {
                    y: 0,
                    duration: 0.4,
                    ease: "power2.out"
                })
                .to(ball, {
                    y: 80,
                    duration: 0.3,
                    ease: "power2.in"
                })
                .to(ball, {
                    y: 0,
                    duration: 0.25,
                    ease: "power2.out"
                })
                .to(ball, {
                    y: 40,
                    duration: 0.2,
                    ease: "power2.in"
                })
                .to(ball, {
                    y: 0,
                    duration: 0.15,
                    ease: "power2.out"
                });
            }
        },
        {
            id: 'liquid-morph',
            title: 'Liquid Morph',
            description: 'Smooth liquid-like morphing between shapes',
            category: 'gsap',
            tags: ['liquid', 'morph', 'smooth', 'organic'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-shape" id="liquid-${AnimationUtils.generateId()}"
                             style="border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;"></div>
                    </div>
                `;

                const shape = container.querySelector('.demo-shape');

                const morphs = [
                    "30% 70% 70% 30% / 30% 30% 70% 70%",
                    "70% 30% 30% 70% / 70% 70% 30% 30%",
                    "50% 50% 50% 50% / 50% 50% 50% 50%",
                    "20% 80% 80% 20% / 80% 20% 20% 80%",
                    "80% 20% 20% 80% / 20% 80% 80% 20%"
                ];

                const tl = gsap.timeline({ repeat: -1 });
                morphs.forEach(morph => {
                    tl.to(shape, {
                        borderRadius: morph,
                        duration: 1,
                        ease: "power2.inOut"
                    });
                });
            }
        },
        {
            id: 'particle-explosion',
            title: 'Particle Explosion',
            description: 'Explosive particle animation with random directions',
            category: 'gsap',
            tags: ['particles', 'explosion', 'random', 'burst'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview" style="position: relative; overflow: hidden;">
                        <div class="demo-button" id="explode-${AnimationUtils.generateId()}">Click to Explode</div>
                    </div>
                `;

                const button = container.querySelector('.demo-button');

                button.addEventListener('click', () => {
                    // Create particles
                    for (let i = 0; i < 20; i++) {
                        const particle = document.createElement('div');
                        particle.style.position = 'absolute';
                        particle.style.width = '4px';
                        particle.style.height = '4px';
                        particle.style.backgroundColor = AnimationUtils.randomColor();
                        particle.style.borderRadius = '50%';
                        particle.style.left = '50%';
                        particle.style.top = '50%';
                        particle.style.pointerEvents = 'none';

                        container.appendChild(particle);

                        const angle = (i / 20) * Math.PI * 2;
                        const distance = AnimationUtils.randomBetween(50, 100);
                        const x = Math.cos(angle) * distance;
                        const y = Math.sin(angle) * distance;

                        gsap.to(particle, {
                            x: x,
                            y: y,
                            scale: 0,
                            opacity: 0,
                            duration: AnimationUtils.randomBetween(0.5, 1.5),
                            ease: "power2.out",
                            onComplete: () => {
                                if (particle.parentNode) {
                                    particle.parentNode.removeChild(particle);
                                }
                            }
                        });
                    }
                });
            }
        },
        {
            id: 'text-split-reveal',
            title: 'Text Split Reveal',
            description: 'Text splits and reveals with complex animation',
            category: 'gsap',
            tags: ['text', 'split', 'reveal', 'complex'],
            init: function(container) {
                container.innerHTML = `
                    <div class="animation-preview">
                        <div class="demo-text" id="split-${AnimationUtils.generateId()}">SPLIT</div>
                    </div>
                `;

                const textElement = container.querySelector('.demo-text');
                const text = textElement.textContent;

                // Split text into individual letters
                textElement.innerHTML = text.split('').map(char =>
                    `<span style="display: inline-block; opacity: 0; transform: translateY(50px) rotateX(90deg);">${char}</span>`
                ).join('');

                const letters = textElement.querySelectorAll('span');

                const tl = gsap.timeline({ repeat: -1, repeatDelay: 2 });
                tl.to(letters, {
                    opacity: 1,
                    y: 0,
                    rotationX: 0,
                    duration: 0.5,
                    stagger: 0.1,
                    ease: "back.out(1.7)"
                })
                .to(letters, {
                    opacity: 0,
                    y: -50,
                    rotationX: -90,
                    duration: 0.3,
                    stagger: 0.05,
                    ease: "power2.in"
                }, "+=1");
            }
        }
    ];

    // Register all GSAP animations
    gsapAnimations.forEach(animation => {
        window.animationRegistry.register(animation);
    });

})();
