// Main Application
class AnimationShowcase {
    constructor() {
        this.currentFilter = 'all';
        this.animationsGrid = AnimationUtils.$('#animationsGrid');
        this.filterTabs = AnimationUtils.$$('.filter-tab');
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.initializeAnimations();
        this.renderAnimations();
        this.setupScrollAnimations();
    }
    
    setupEventListeners() {
        // Filter tabs
        this.filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.handleFilterChange(e.target.dataset.filter);
            });
        });
        
        // Smooth scrolling for navigation
        document.addEventListener('click', (e) => {
            if (e.target.matches('a[href^="#"]')) {
                e.preventDefault();
                const target = document.querySelector(e.target.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            }
        });
        
        // Header scroll effect
        let lastScrollY = window.scrollY;
        window.addEventListener('scroll', AnimationUtils.throttle(() => {
            const header = AnimationUtils.$('.header');
            const currentScrollY = window.scrollY;
            
            if (currentScrollY > 100) {
                header.style.transform = currentScrollY > lastScrollY ? 'translateY(-100%)' : 'translateY(0)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScrollY = currentScrollY;
        }, 100));
    }
    
    initializeAnimations() {
        // This will be called by individual animation modules
        // Each module will register its animations with the registry
    }
    
    handleFilterChange(filter) {
        this.currentFilter = filter;
        
        // Update active tab
        this.filterTabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.filter === filter);
        });
        
        // Filter and render animations
        this.renderAnimations();
    }
    
    renderAnimations() {
        const animations = this.currentFilter === 'all'
            ? window.animationRegistry.getAll()
            : window.animationRegistry.getByCategory(this.currentFilter);

        // Clear grid with performance optimization
        this.animationsGrid.innerHTML = '';

        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();

        // Render animations
        animations.forEach((animation, index) => {
            const card = this.createAnimationCard(animation);
            fragment.appendChild(card);
        });

        // Append all at once
        this.animationsGrid.appendChild(fragment);

        // Animate cards with intersection observer for performance
        if (!AnimationUtils.prefersReducedMotion()) {
            const cards = this.animationsGrid.querySelectorAll('.animation-card');
            cards.forEach((card, index) => {
                // Stagger animation entrance
                gsap.fromTo(card,
                    {
                        opacity: 0,
                        y: 50,
                        scale: 0.9
                    },
                    {
                        opacity: 1,
                        y: 0,
                        scale: 1,
                        duration: 0.6,
                        delay: Math.min(index * 0.05, 1), // Cap delay for performance
                        ease: "power2.out"
                    }
                );
            });
        }

        // Update animation count
        this.updateAnimationCount(animations.length);
    }

    updateAnimationCount(count) {
        // Update the stats in the hero section
        const statNumber = document.querySelector('.stat__number');
        if (statNumber && statNumber.textContent.includes('+')) {
            gsap.to({ value: parseInt(statNumber.textContent) }, {
                value: count,
                duration: 0.5,
                ease: 'power2.out',
                onUpdate: function() {
                    statNumber.textContent = Math.round(this.targets()[0].value) + '+';
                }
            });
        }
    }
    
    createAnimationCard(animation) {
        const card = AnimationUtils.createElement('div', 'animation-card');
        card.dataset.category = animation.category;
        card.dataset.id = animation.id;
        
        card.innerHTML = `
            <div class="animation-card__preview" id="preview-${animation.id}">
                <!-- Animation preview will be rendered here -->
            </div>
            <div class="animation-card__content">
                <h3 class="animation-card__title">${animation.title}</h3>
                <p class="animation-card__description">${animation.description}</p>
                <div class="animation-card__tags">
                    ${animation.tags.map(tag => `<span class="animation-tag">${tag}</span>`).join('')}
                </div>
            </div>
        `;
        
        // Initialize the animation in the preview area
        const previewElement = card.querySelector(`#preview-${animation.id}`);
        if (animation.init && typeof animation.init === 'function') {
            animation.init(previewElement);
        }
        
        return card;
    }
    
    setupScrollAnimations() {
        // Hero title animation
        const heroTitle = AnimationUtils.$('.hero__title');
        if (heroTitle) {
            const titleLines = heroTitle.querySelectorAll('.hero__title-line');
            gsap.fromTo(titleLines,
                { 
                    opacity: 0, 
                    y: 100,
                    rotationX: 90
                },
                { 
                    opacity: 1, 
                    y: 0,
                    rotationX: 0,
                    duration: 1,
                    stagger: 0.2,
                    ease: "power3.out",
                    delay: 0.5
                }
            );
        }
        
        // Hero subtitle animation
        const heroSubtitle = AnimationUtils.$('.hero__subtitle');
        if (heroSubtitle) {
            gsap.fromTo(heroSubtitle,
                { 
                    opacity: 0, 
                    y: 50
                },
                { 
                    opacity: 1, 
                    y: 0,
                    duration: 0.8,
                    delay: 1.2,
                    ease: "power2.out"
                }
            );
        }
        
        // Hero stats animation
        const heroStats = AnimationUtils.$$('.stat');
        if (heroStats.length) {
            gsap.fromTo(heroStats,
                { 
                    opacity: 0, 
                    y: 30,
                    scale: 0.8
                },
                { 
                    opacity: 1, 
                    y: 0,
                    scale: 1,
                    duration: 0.6,
                    stagger: 0.1,
                    delay: 1.5,
                    ease: "back.out(1.7)"
                }
            );
        }
        
        // Filter tabs animation
        const filterTabs = AnimationUtils.$$('.filter-tab');
        if (filterTabs.length) {
            gsap.fromTo(filterTabs,
                { 
                    opacity: 0, 
                    y: 20,
                    scale: 0.9
                },
                { 
                    opacity: 1, 
                    y: 0,
                    scale: 1,
                    duration: 0.5,
                    stagger: 0.05,
                    ease: "power2.out",
                    scrollTrigger: {
                        trigger: '.filters',
                        start: 'top 80%',
                        end: 'bottom 20%',
                        toggleActions: 'play none none reverse'
                    }
                }
            );
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check for reduced motion preference
    if (AnimationUtils.prefersReducedMotion()) {
        // Disable complex animations for users who prefer reduced motion
        gsap.globalTimeline.timeScale(0.1);
    }
    
    // Initialize the showcase
    window.animationShowcase = new AnimationShowcase();
    
    // Initialize smooth scrolling with Lenis
    const lenis = new Lenis({
        duration: 1.2,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        direction: 'vertical',
        gestureDirection: 'vertical',
        smooth: true,
        mouseMultiplier: 1,
        smoothTouch: false,
        touchMultiplier: 2,
        infinite: false,
    });
    
    function raf(time) {
        lenis.raf(time);
        requestAnimationFrame(raf);
    }
    
    requestAnimationFrame(raf);
    
    // Connect Lenis with GSAP ScrollTrigger
    lenis.on('scroll', ScrollTrigger.update);
    
    gsap.ticker.add((time) => {
        lenis.raf(time * 1000);
    });
    
    gsap.ticker.lagSmoothing(0);
});
