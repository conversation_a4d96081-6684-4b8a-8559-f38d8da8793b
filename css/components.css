/* Component Styles for Animation Previews */

/* Base animation preview styles */
.animation-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

/* Button Components */
.demo-button {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    border: 2px solid var(--color-primary);
    border-radius: var(--radius-md);
    background-color: transparent;
    color: var(--color-primary);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.demo-button--filled {
    background-color: var(--color-primary);
    color: var(--color-white);
}

/* Card Components */
.demo-card {
    width: 120px;
    height: 80px;
    background-color: var(--color-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    transition: all var(--transition-base);
    cursor: pointer;
}

/* Text Components */
.demo-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    text-align: center;
}

.demo-text--large {
    font-size: var(--font-size-2xl);
}

.demo-text--gradient {
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Shape Components */
.demo-shape {
    width: 60px;
    height: 60px;
    background-color: var(--color-primary);
    border-radius: var(--radius-md);
    transition: all var(--transition-base);
}

.demo-shape--circle {
    border-radius: 50%;
}

.demo-shape--small {
    width: 40px;
    height: 40px;
}

.demo-shape--large {
    width: 80px;
    height: 80px;
}

/* Icon Components */
.demo-icon {
    width: 24px;
    height: 24px;
    fill: currentColor;
    transition: all var(--transition-base);
}

.demo-icon--large {
    width: 32px;
    height: 32px;
}

/* Progress Components */
.demo-progress {
    width: 200px;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.demo-progress__bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
    border-radius: var(--radius-full);
    transition: width var(--transition-base);
}

/* Loader Components */
.demo-loader {
    width: 40px;
    height: 40px;
    position: relative;
}

.demo-loader__spinner {
    width: 100%;
    height: 100%;
    border: 3px solid var(--bg-tertiary);
    border-top: 3px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Input Components */
.demo-input {
    width: 200px;
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
    border: 2px solid var(--border-medium);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: all var(--transition-base);
}

.demo-input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* Badge Components */
.demo-badge {
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    background-color: var(--color-primary);
    color: var(--color-white);
    border-radius: var(--radius-full);
    display: inline-block;
}

.demo-badge--secondary {
    background-color: var(--color-gray-500);
}

.demo-badge--success {
    background-color: var(--color-success);
}

.demo-badge--warning {
    background-color: var(--color-warning);
}

.demo-badge--error {
    background-color: var(--color-error);
}

/* Avatar Components */
.demo-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-white);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
}

.demo-avatar--small {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-sm);
}

.demo-avatar--large {
    width: 64px;
    height: 64px;
    font-size: var(--font-size-xl);
}

/* Toggle Components */
.demo-toggle {
    width: 48px;
    height: 24px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-full);
    position: relative;
    cursor: pointer;
    transition: background-color var(--transition-base);
}

.demo-toggle__handle {
    width: 20px;
    height: 20px;
    background-color: var(--color-white);
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: transform var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.demo-toggle.active {
    background-color: var(--color-primary);
}

.demo-toggle.active .demo-toggle__handle {
    transform: translateX(24px);
}

/* Notification Components */
.demo-notification {
    width: 250px;
    padding: var(--space-4);
    background-color: var(--color-white);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.demo-notification__title {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.demo-notification__message {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

/* Tooltip Components */
.demo-tooltip {
    position: relative;
    display: inline-block;
}

.demo-tooltip__content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: var(--space-2) var(--space-3);
    background-color: var(--color-gray-900);
    color: var(--color-white);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-md);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    margin-bottom: var(--space-2);
}

.demo-tooltip__content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--color-gray-900);
}

.demo-tooltip:hover .demo-tooltip__content {
    opacity: 1;
    visibility: visible;
}

/* Grid Layout for Multiple Elements */
.demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: var(--space-4);
    width: 100%;
    max-width: 300px;
}

.demo-flex {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* Animation States */
.animate-on-hover:hover {
    transform: scale(1.05);
}

.animate-on-click:active {
    transform: scale(0.95);
}

/* Utility Classes for Animations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

.will-change-auto {
    will-change: auto;
}
