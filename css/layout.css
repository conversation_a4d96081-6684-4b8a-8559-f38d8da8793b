/* Layout Styles */

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: var(--z-fixed);
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-light);
    transition: all var(--transition-fast);
}

.header__content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.header__logo {
    display: flex;
    align-items: center;
}

.logo-text {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header__nav {
    display: flex;
    align-items: center;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    width: 24px;
    height: 24px;
    cursor: pointer;
}

.nav-toggle span {
    width: 100%;
    height: 2px;
    background-color: var(--text-primary);
    margin: 2px 0;
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    padding: calc(70px + var(--space-20)) 0 var(--space-20);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.hero__content {
    text-align: center;
    position: relative;
    z-index: 1;
}

.hero__title {
    font-size: clamp(var(--font-size-4xl), 8vw, var(--font-size-6xl));
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-6);
    color: var(--text-primary);
}

.hero__title-line {
    display: block;
}

.hero__subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto var(--space-12);
    line-height: var(--line-height-relaxed);
}

.hero__stats {
    display: flex;
    justify-content: center;
    gap: var(--space-12);
    flex-wrap: wrap;
}

.stat {
    text-align: center;
}

.stat__number {
    display: block;
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--color-primary);
    line-height: 1;
}

.stat__label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-top: var(--space-1);
}

/* Filters Section */
.filters {
    padding: var(--space-16) 0;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-light);
}

.filters__content {
    text-align: center;
}

.filters__title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--space-8);
    color: var(--text-primary);
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.filter-tab {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-full);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.filter-tab:hover {
    color: var(--color-primary);
    border-color: var(--color-primary);
}

.filter-tab.active {
    color: var(--color-white);
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

/* Animations Grid */
.animations {
    padding: var(--space-20) 0;
    background-color: var(--bg-primary);
}

.animations__grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: var(--space-8);
    margin-top: var(--space-8);
}

/* Animation Card */
.animation-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: all var(--transition-base);
    box-shadow: var(--shadow-sm);
}

.animation-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary);
}

.animation-card__preview {
    height: 200px;
    background-color: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.animation-card__content {
    padding: var(--space-6);
}

.animation-card__title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.animation-card__description {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    margin-bottom: var(--space-4);
    line-height: var(--line-height-relaxed);
}

.animation-card__tags {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.animation-tag {
    padding: var(--space-1) var(--space-3);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-primary);
    background-color: rgba(99, 102, 241, 0.1);
    border-radius: var(--radius-full);
    white-space: nowrap;
}

/* Footer */
.footer {
    padding: var(--space-16) 0;
    background-color: var(--bg-dark);
    color: var(--text-inverse);
}

.footer__content {
    text-align: center;
}

.footer__text {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-6);
    color: var(--color-gray-300);
}

.footer__tech {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
    flex-wrap: wrap;
}

.tech-badge {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-300);
    background-color: var(--color-gray-800);
    border: 1px solid var(--color-gray-700);
    border-radius: var(--radius-md);
}

/* Performance Optimizations */
.animation-card {
    will-change: transform;
    contain: layout style paint;
}

.animation-card__preview {
    will-change: transform;
    contain: layout style paint;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    .animation-card {
        transition: none;
    }

    .animation-card:hover {
        transform: none;
    }

    .filter-tab {
        transition: none;
    }

    .demo-button,
    .demo-card,
    .demo-shape {
        animation: none !important;
        transition: none !important;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .animations__grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: var(--space-6);
    }
}

@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }

    .hero {
        padding: calc(70px + var(--space-12)) 0 var(--space-12);
    }

    .hero__stats {
        gap: var(--space-8);
    }

    .filters {
        padding: var(--space-12) 0;
    }

    .animations {
        padding: var(--space-12) 0;
    }

    .animations__grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }

    .filter-tabs {
        gap: var(--space-1);
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: var(--space-2);
    }

    .filter-tab {
        padding: var(--space-2) var(--space-4);
        font-size: var(--font-size-xs);
        flex-shrink: 0;
    }

    .animation-card__preview {
        height: 150px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-3);
    }

    .hero__title {
        font-size: var(--font-size-3xl);
    }

    .hero__subtitle {
        font-size: var(--font-size-base);
    }

    .hero__stats {
        flex-direction: column;
        gap: var(--space-6);
    }

    .footer__tech {
        gap: var(--space-2);
    }

    .animation-card__content {
        padding: var(--space-4);
    }

    .animation-card__preview {
        height: 120px;
    }

    .filter-tabs {
        padding: 0 var(--space-2);
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .demo-shape,
    .demo-button,
    .demo-card {
        transform: translateZ(0);
        backface-visibility: hidden;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-primary: var(--color-gray-900);
        --bg-secondary: var(--color-gray-800);
        --bg-tertiary: var(--color-gray-700);
        --text-primary: var(--color-gray-100);
        --text-secondary: var(--color-gray-300);
        --text-muted: var(--color-gray-400);
        --border-light: var(--color-gray-700);
        --border-medium: var(--color-gray-600);
    }

    .header {
        background-color: rgba(17, 24, 39, 0.95);
        border-bottom-color: var(--color-gray-700);
    }

    .animation-card {
        background-color: var(--color-gray-800);
        border-color: var(--color-gray-700);
    }

    .animation-card:hover {
        border-color: var(--color-primary);
    }
}
