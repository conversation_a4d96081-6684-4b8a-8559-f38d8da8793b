/* CSS-Only Animations */

/* 1. Hover Grow */
.hover-grow {
    transition: transform var(--transition-base);
}

.hover-grow:hover {
    transform: scale(1.1);
}

/* 2. Hover Shrink */
.hover-shrink {
    transition: transform var(--transition-base);
}

.hover-shrink:hover {
    transform: scale(0.9);
}

/* 3. Hover Rotate */
.hover-rotate {
    transition: transform var(--transition-base);
}

.hover-rotate:hover {
    transform: rotate(15deg);
}

/* 4. Hover Skew */
.hover-skew {
    transition: transform var(--transition-base);
}

.hover-skew:hover {
    transform: skew(-10deg, 0);
}

/* 5. Hover Float */
.hover-float {
    transition: transform var(--transition-base);
}

.hover-float:hover {
    transform: translateY(-10px);
}

/* 6. Hover Sink */
.hover-sink {
    transition: transform var(--transition-base);
}

.hover-sink:hover {
    transform: translateY(10px);
}

/* 7. Hover Shadow */
.hover-shadow {
    transition: box-shadow var(--transition-base);
}

.hover-shadow:hover {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* 8. Hover Glow */
.hover-glow {
    transition: box-shadow var(--transition-base);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.6);
}

/* 9. Hover Border Radius */
.hover-border-radius {
    transition: border-radius var(--transition-base);
    border-radius: var(--radius-md);
}

.hover-border-radius:hover {
    border-radius: var(--radius-2xl);
}

/* 10. Hover Color Change */
.hover-color-change {
    transition: background-color var(--transition-base), color var(--transition-base);
}

.hover-color-change:hover {
    background-color: var(--color-accent);
    color: var(--color-white);
}

/* 11. Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
}

/* 12. Bounce Animation */
.bounce {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-20px);
    }
    70% {
        transform: translateY(-10px);
    }
    90% {
        transform: translateY(-4px);
    }
}

/* 13. Shake Animation */
.shake {
    animation: shake 0.5s infinite;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* 14. Wobble Animation */
.wobble {
    animation: wobble 1s infinite;
}

@keyframes wobble {
    0%, 100% {
        transform: translateX(0%) rotate(0deg);
    }
    15% {
        transform: translateX(-25%) rotate(-5deg);
    }
    30% {
        transform: translateX(20%) rotate(3deg);
    }
    45% {
        transform: translateX(-15%) rotate(-3deg);
    }
    60% {
        transform: translateX(10%) rotate(2deg);
    }
    75% {
        transform: translateX(-5%) rotate(-1deg);
    }
}

/* 15. Swing Animation */
.swing {
    animation: swing 1s infinite;
    transform-origin: top center;
}

@keyframes swing {
    20% {
        transform: rotate(15deg);
    }
    40% {
        transform: rotate(-10deg);
    }
    60% {
        transform: rotate(5deg);
    }
    80% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

/* 16. Fade In */
.fade-in {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* 17. Fade In Up */
.fade-in-up {
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 18. Fade In Down */
.fade-in-down {
    animation: fadeInDown 1s ease-out;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 19. Fade In Left */
.fade-in-left {
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 20. Fade In Right */
.fade-in-right {
    animation: fadeInRight 1s ease-out;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 21. Zoom In */
.zoom-in {
    animation: zoomIn 0.5s ease-out;
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
    }
    to {
        transform: scale(1);
    }
}

/* 22. Zoom Out */
.zoom-out {
    animation: zoomOut 0.5s ease-out;
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(0.3);
    }
    to {
        opacity: 0;
        transform: scale(0.3);
    }
}

/* 23. Flip In X */
.flip-in-x {
    animation: flipInX 1s ease-out;
}

@keyframes flipInX {
    from {
        transform: perspective(400px) rotateX(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        transform: perspective(400px) rotateX(0deg);
        opacity: 1;
    }
}

/* 24. Flip In Y */
.flip-in-y {
    animation: flipInY 1s ease-out;
}

@keyframes flipInY {
    from {
        transform: perspective(400px) rotateY(90deg);
        opacity: 0;
    }
    40% {
        transform: perspective(400px) rotateY(-20deg);
    }
    60% {
        transform: perspective(400px) rotateY(10deg);
        opacity: 1;
    }
    80% {
        transform: perspective(400px) rotateY(-5deg);
    }
    to {
        transform: perspective(400px) rotateY(0deg);
        opacity: 1;
    }
}

/* 25. Rotate In */
.rotate-in {
    animation: rotateIn 1s ease-out;
}

@keyframes rotateIn {
    from {
        transform: rotate(-200deg);
        opacity: 0;
    }
    to {
        transform: rotate(0);
        opacity: 1;
    }
}

/* Additional CSS-Only Animations */

/* 26. Hover Slide */
.hover-slide {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.hover-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-base);
}

.hover-slide:hover::before {
    left: 100%;
}

/* 27. Hover Underline */
.hover-underline {
    position: relative;
    text-decoration: none;
}

.hover-underline::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-primary);
    transition: width var(--transition-base);
}

.hover-underline:hover::after {
    width: 100%;
}

/* 28. Hover Fill */
.hover-fill {
    position: relative;
    overflow: hidden;
    transition: color var(--transition-base);
}

.hover-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background-color: var(--color-primary);
    transition: width var(--transition-base);
    z-index: -1;
}

.hover-fill:hover {
    color: var(--color-white);
}

.hover-fill:hover::before {
    width: 100%;
}

/* 29. Hover Tilt */
.hover-tilt {
    transition: transform var(--transition-base);
}

.hover-tilt:hover {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

/* 30. Hover Flip */
.hover-flip {
    transition: transform var(--transition-base);
}

.hover-flip:hover {
    transform: rotateY(180deg);
}

/* 31. Heartbeat Animation */
.heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 50%, 100% {
        transform: scale(1);
    }
    5%, 45% {
        transform: scale(1.1);
    }
    10%, 40% {
        transform: scale(1.2);
    }
    15%, 35% {
        transform: scale(1.1);
    }
    20%, 30% {
        transform: scale(1);
    }
}

/* 32. Flash Animation */
.flash {
    animation: flash 2s infinite;
}

@keyframes flash {
    0%, 50%, 100% {
        opacity: 1;
    }
    25%, 75% {
        opacity: 0;
    }
}

/* 33. Rubber Band Animation */
.rubber-band {
    animation: rubberBand 1s ease-out;
}

@keyframes rubberBand {
    from {
        transform: scale3d(1, 1, 1);
    }
    30% {
        transform: scale3d(1.25, 0.75, 1);
    }
    40% {
        transform: scale3d(0.75, 1.25, 1);
    }
    50% {
        transform: scale3d(1.15, 0.85, 1);
    }
    65% {
        transform: scale3d(0.95, 1.05, 1);
    }
    75% {
        transform: scale3d(1.05, 0.95, 1);
    }
    to {
        transform: scale3d(1, 1, 1);
    }
}

/* 34. Jello Animation */
.jello {
    animation: jello 1s ease-out;
    transform-origin: center;
}

@keyframes jello {
    from, 11.1%, to {
        transform: translate3d(0, 0, 0);
    }
    22.2% {
        transform: skewX(-12.5deg) skewY(-12.5deg);
    }
    33.3% {
        transform: skewX(6.25deg) skewY(6.25deg);
    }
    44.4% {
        transform: skewX(-3.125deg) skewY(-3.125deg);
    }
    55.5% {
        transform: skewX(1.5625deg) skewY(1.5625deg);
    }
    66.6% {
        transform: skewX(-0.78125deg) skewY(-0.78125deg);
    }
    77.7% {
        transform: skewX(0.390625deg) skewY(0.390625deg);
    }
    88.8% {
        transform: skewX(-0.1953125deg) skewY(-0.1953125deg);
    }
}

/* 35. Tada Animation */
.tada {
    animation: tada 1s ease-in-out;
}

@keyframes tada {
    from {
        transform: scale3d(1, 1, 1);
    }
    10%, 20% {
        transform: scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
    }
    30%, 50%, 70%, 90% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
    }
    40%, 60%, 80% {
        transform: scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
    }
    to {
        transform: scale3d(1, 1, 1);
    }
}

/* 36. Hover Buzz */
.hover-buzz {
    transition: transform var(--transition-fast);
}

.hover-buzz:hover {
    animation: buzz 0.15s linear infinite;
}

@keyframes buzz {
    50% {
        transform: translateX(3px) rotate(2deg);
    }
    100% {
        transform: translateX(-3px) rotate(-2deg);
    }
}

/* 37. Hover Forward */
.hover-forward {
    transition: transform var(--transition-base);
}

.hover-forward:hover {
    transform: translateX(8px);
}

/* 38. Hover Backward */
.hover-backward {
    transition: transform var(--transition-base);
}

.hover-backward:hover {
    transform: translateX(-8px);
}

/* 39. Hover Bob */
.hover-bob {
    transition: transform var(--transition-base);
}

.hover-bob:hover {
    animation: bob 1s ease-in-out infinite;
}

@keyframes bob {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-8px);
    }
}

/* 40. Hover Hang */
.hover-hang {
    transition: transform var(--transition-base);
}

.hover-hang:hover {
    animation: hang 1s ease-in-out infinite;
    transform-origin: top center;
}

@keyframes hang {
    0%, 100% {
        transform: rotate(0deg);
    }
    20%, 60% {
        transform: rotate(-4deg);
    }
    40%, 80% {
        transform: rotate(4deg);
    }
}
