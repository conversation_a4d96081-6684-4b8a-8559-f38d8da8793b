# 🎨 Web Animation Showcase

A comprehensive collection of **100+ modern web animations and micro-interactions** built with HTML, CSS, and JavaScript. This showcase demonstrates various animation techniques from simple CSS transitions to complex GSAP-powered effects.

## ✨ Features

- **100+ Unique Animations** across 8 different categories
- **Modern & Minimal Design** with responsive layout
- **Performance Optimized** with reduced motion support
- **Multiple Animation Libraries** including GSAP, Anime.js, and more
- **Interactive Previews** with live demonstrations
- **Categorized & Searchable** animations with tags
- **Mobile Responsive** design for all devices

## 🎯 Animation Categories

### 1. CSS-Only Animations (30+ animations)
- Hover effects (grow, shrink, rotate, skew, float, etc.)
- Keyframe animations (pulse, bounce, shake, wobble, etc.)
- Transition effects (fade, slide, zoom, flip, etc.)
- Advanced hover effects (buzz, bob, hang, etc.)

### 2. Text Animations (12+ animations)
- Typewriter effects
- Text reveal animations
- Stagger effects
- Glitch and scramble effects
- Rainbow and neon effects
- Matrix-style digital rain

### 3. Scroll-Based Animations (8+ animations)
- Scroll-triggered reveals
- Parallax effects
- Progress indicators
- Counter animations
- Mask reveals

### 4. Interactive Micro-interactions (11+ animations)
- Magnetic buttons
- Cursor trails and custom cursors
- Ripple effects
- Parallax cards
- Draggable elements
- Particle effects

### 5. Advanced GSAP Animations (6+ animations)
- Morphing shapes
- Stagger animations
- Physics-based effects
- Particle explosions
- Complex timelines

### 6. Loaders & Progress (9+ animations)
- Spinning loaders
- Bouncing dots
- Progress bars
- Skeleton loaders
- Orbit animations
- Wave effects

### 7. Page Transitions (10+ animations)
- Slide transitions
- Fade effects
- Scale transitions
- 3D flip effects
- Wipe and curtain reveals

## 🛠 Technologies Used

- **HTML5** - Semantic markup
- **CSS3** - Custom properties, animations, transforms
- **Vanilla JavaScript** - Core functionality
- **GSAP** - Advanced animations and timelines
- **Lenis** - Smooth scrolling
- **Anime.js** - Lightweight animations
- **SplitType** - Text manipulation

## 🚀 Getting Started

1. **Clone or download** the project
2. **Open `index.html`** in a web browser
3. **Or serve locally** using a simple HTTP server:
   ```bash
   python3 -m http.server 8000
   # Then visit http://localhost:8000
   ```

## 📱 Browser Support

- **Modern browsers** (Chrome, Firefox, Safari, Edge)
- **Mobile responsive** design
- **Accessibility features** including reduced motion support
- **Progressive enhancement** for older browsers

## 🎨 Customization

The project is built with CSS custom properties (variables) making it easy to customize:

- **Colors**: Modify variables in `css/variables.css`
- **Spacing**: Adjust spacing scale in variables
- **Typography**: Change font families and sizes
- **Animations**: Add new animations by following the existing patterns

## 📁 Project Structure

```
├── index.html              # Main HTML file
├── css/
│   ├── reset.css          # CSS reset
│   ├── variables.css      # CSS custom properties
│   ├── base.css           # Base styles
│   ├── layout.css         # Layout and responsive design
│   ├── components.css     # Reusable components
│   └── animations.css     # CSS-only animations
├── js/
│   ├── utils.js           # Utility functions
│   ├── main.js            # Main application logic
│   └── animations/        # Animation modules
│       ├── css-only.js    # CSS animation definitions
│       ├── text.js        # Text animations
│       ├── scroll.js      # Scroll-based animations
│       ├── interactive.js # Interactive animations
│       ├── gsap.js        # GSAP animations
│       ├── loaders.js     # Loading animations
│       └── transitions.js # Page transitions
└── README.md              # This file
```

## 🎯 Performance Features

- **Reduced motion support** for accessibility
- **Efficient DOM manipulation** with document fragments
- **Optimized animations** with `will-change` properties
- **Responsive images** and lazy loading considerations
- **Minimal JavaScript** for core functionality

## 🔧 Adding New Animations

To add a new animation:

1. **Define the animation** in the appropriate module file
2. **Register it** with the animation registry
3. **Add CSS** if needed in the animations.css file
4. **Follow the existing pattern** for consistency

Example:
```javascript
{
    id: 'my-animation',
    title: 'My Animation',
    description: 'Description of what it does',
    category: 'css-only',
    tags: ['hover', 'transform', 'scale'],
    init: function(container) {
        // Animation implementation
    }
}
```

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Feel free to:
- Add new animations
- Improve existing ones
- Fix bugs
- Enhance documentation
- Suggest new features

---

**Built with ❤️ for the web animation community**
